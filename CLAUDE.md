# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Go-Mail is a temporary email service platform built with Go backend and Vue.js frontend. It provides disposable email addresses using Mail.com's alias functionality, with focus on privacy protection and development testing scenarios.

## Architecture

### Three-Tier Architecture
- **Backend** (`go-mail-backend/`): Go + Gin + SQLite - Core email management API
- **Frontend** (`mail-frontend/`): Vue3 + TypeScript + Naive UI - Admin dashboard
- **Client** (`go-wails3-mail-client/`): Wails3 desktop app (on hold)

### Core Components

#### Backend (go-mail-backend)
- **Manager Layer**: `internal/manager/` - Main email management orchestration
- **Client Layer**: `internal/mail/client/` - Mail.com integration and HTTP client
- **Pool System**: `internal/pool/` - Account and session resource management
- **Database**: `internal/database/` - SQLite operations with 7 core tables
- **API Layer**: `internal/api/` - HTTP REST API with 31 endpoints
- **Services**: `internal/services/` - Business logic (activation, mailbox, monitor)
- **Scheduler**: `internal/scheduler/` - Background task system
- **Auth**: `internal/auth/` - JWT and AES encryption security

#### Frontend (mail-frontend)
- **Vue3 + TypeScript** with Composition API
- **Naive UI** component library
- **Pinia** for state management
- **Vue Router** for navigation
- **Axios** for HTTP requests
- **ECharts** for data visualization

## Development Commands

### Backend Development
```bash
# Navigate to backend
cd go-mail-backend

# Run development server
go run ./cmd/server/main.go

# Build for production
go build -o bin/go-mail-server ./cmd/server/main.go

# Run with custom configuration
go run ./cmd/server/main.go -host=0.0.0.0 -port=8080 -debug=true

# Run single login test
go run ./cmd/single-login/main.go

# Run batch login test
go run ./cmd/batch-login/main.go

# View version information
go run ./cmd/server/main.go -version
```

### Frontend Development
```bash
# Navigate to frontend
cd mail-frontend

# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Lint and format
npm run lint
npm run format
```

### Wails3 Client Development
```bash
# Navigate to client
cd go-wails3-mail-client

# Development mode
wails3 dev

# Build application
wails3 build

# Using Task runner
task dev          # Development mode
task build        # Build application
task run          # Run application
```

## Configuration

### Backend Configuration
The backend uses command-line flags and defaults:
- **Host**: `0.0.0.0` (configurable with `-host`)
- **Port**: `8080` (configurable with `-port`)
- **Database**: `./go-mail-data/data/go-mail.db`
- **Accounts File**: `./go-mail-data/data/accounts.json`
- **JWT Secret**: Default configured in `DefaultConfig()`
- **AES Key**: Default configured in `DefaultConfig()`

### Frontend Configuration
Environment variables in `.env.development` and `.env.production`:
- `VITE_API_BASE_URL`: Backend API endpoint
- `VITE_APP_TITLE`: Application title
- `VITE_APP_VERSION`: Application version

## Database Schema

### Core Tables
1. **accounts**: Mail.com account credentials and status
2. **login_records**: Authentication attempt logging
3. **activation_codes**: License activation system
4. **temp_mailboxes**: Temporary email allocations
5. **mail_records**: Email message tracking
6. **system_logs**: System operation logging
7. **task_logs**: Background task execution records

## API Endpoints

### Authentication (Admin)
- `POST /api/v1/auth/login` - Admin login
- `POST /api/v1/auth/refresh` - Token refresh

### Activation Codes
- `POST /api/v1/activation/generate` - Batch generate codes
- `POST /api/v1/activation/verify` - Verify code
- `GET /api/v1/activation/list` - List codes

### Client API
- `POST /api/v1/client/activation/verify` - Client activation
- `POST /api/v1/client/mailbox/allocate` - Allocate temporary mailbox
- `POST /api/v1/client/mailbox/query` - Query emails
- `POST /api/v1/client/mailbox/release` - Release mailbox

### System Monitoring
- `GET /api/v1/monitor/accounts` - Account status
- `GET /api/v1/monitor/sessions` - Session status
- `GET /api/v1/monitor/statistics` - System stats

## Security Features

### Authentication
- **JWT tokens** for admin authentication
- **AES-256-GCM encryption** for client communication
- **Device fingerprinting** for activation binding
- **HMAC signatures** for data integrity

### Device Binding
Combines system UUID, username, computer name, platform, and MAC address to create unique device fingerprints.

### Rate Limiting
Built-in rate limiting middleware to prevent abuse.

## Development Workflow

### Adding New Features
1. **Backend**: Add handlers in `internal/api/handlers/`, update routes
2. **Database**: Add models in `internal/database/models.go`
3. **Business Logic**: Implement in `internal/services/`
4. **Frontend**: Add API calls in `src/api/`, create views in `src/views/`

### Testing Integration
- Test endpoints individually before frontend integration
- Use the detailed logging system for debugging
- Monitor the scheduler for background task issues

## Key Files to Understand

- `go-mail-backend/cmd/server/main.go` - Main server entry point
- `go-mail-backend/internal/manager/mail_manager.go` - Core business logic
- `go-mail-backend/internal/api/controller/server.go` - API server setup
- `mail-frontend/src/api/` - Frontend API integration
- `docs/产品需求文档.md` - Product requirements
- `docs/技术实现方案.md` - Technical implementation

## Testing and Debugging

### Testing Mail.com Integration
- Use `go run ./cmd/single-login/main.go` to test individual account connectivity
- Use `go run ./cmd/batch-login/main.go` to test multiple account connectivity
- Check `go-mail-data/go-mail.log` for detailed operation logs

### Database Management
- SQLite database auto-creates on first run in `go-mail-data/data/`
- Use any SQLite browser to inspect the database
- Schema updates are handled automatically by the application

### Frontend Development
- Development server runs on `http://localhost:3000`
- API requests proxy to backend at `http://localhost:8080`
- Hot reload enabled for both frontend and backend changes

## Common Issues

### Mail.com Integration
- If email parsing fails, check `internal/mail/client/mail_client.go`
- Session issues are logged in the task system
- Account health is monitored by background tasks
- Proxy configuration can be set in account files for testing

### Database Migrations
- SQLite database auto-creates on first run
- Schema updates are handled in `internal/database/migration.go`

### Frontend API Calls
- All client communication is AES encrypted
- Device fingerprint must be included in headers
- Activation code required for client operations

## Project Status

**Completion**: ~95% complete
- **Backend**: 100% complete with 31 API endpoints
- **Frontend**: 100% complete with admin dashboard
- **Mail.com Integration**: 95% complete (minor optimizations needed)
- **Client App**: On hold (not priority)

## Dependencies and Tech Stack

### Backend Dependencies
- **Go 1.21**: Core language
- **Gin 1.9.1**: HTTP web framework
- **JWT 5.2.0**: JSON Web Token implementation
- **SQLite**: Database storage
- **AES-256-GCM**: Encryption for client communications

### Frontend Dependencies
- **Vue 3.5.17**: Frontend framework
- **TypeScript 5.7.2**: Type safety
- **Naive UI 2.40.1**: Component library
- **Pinia 2.3.0**: State management
- **ECharts 5.5.1**: Data visualization

### Development Tools
- **Vite 6.0.5**: Build tool and dev server
- **ESLint + Prettier**: Code quality and formatting
- **Wails3**: Desktop application framework (client app)