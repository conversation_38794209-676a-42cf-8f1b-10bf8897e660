# Go-Mail Development Guide

## Build Commands

### Backend (Go)
```bash
cd go-mail-backend

# Development server
go run ./cmd/server/main.go

# Build for production
go build -o bin/go-mail-server.exe ./cmd/server/main.go

# Run with custom config
go run ./cmd/server/main.go -host=0.0.0.0 -port=8080 -debug=true

# Test single login
go run ./cmd/single-login/main.go

# Test batch login  
go run ./cmd/batch-login/main.go
```

### Frontend (Vue.js)
```bash
cd mail-frontend

# Development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Lint and format
npm run lint
npm run format
```

### Wails3 Client
```bash
cd go-wails3-mail-client

# Development mode
wails3 dev

# Build application
wails3 build

# Using Task runner
task dev          # Development mode
task build        # Build application
task run          # Run application
```

## Code Style Guidelines

### Go Backend
- **Imports**: Group standard library, third-party, and local packages
- **Naming**: Use camelCase for variables, PascalCase for exported types/functions
- **Error Handling**: Always check errors, use structured error responses
- **Comments**: Chinese comments for business logic, English for technical details
- **Formatting**: Use standard Go formatting (`go fmt`)

### TypeScript Frontend  
- **Imports**: Use absolute imports with `@/` prefix
- **Naming**: camelCase for variables/functions, PascalCase for types/interfaces
- **Types**: Always use TypeScript types, prefer interface over type
- **Formatting**: Prettier config: no semicolons, single quotes, 2-space indentation
- **Vue**: Use Composition API with `<script setup>`

### Error Handling
- **Backend**: Return structured APIResponse with Code, Message, Error, Timestamp
- **Frontend**: Use try/catch with proper error logging and user feedback
- **Validation**: Validate all inputs, return appropriate HTTP status codes

### Testing
- No test framework currently configured
- Use manual testing via API endpoints and UI interaction
- Check logs in `go-mail-data/log/` for debugging

### Database
- SQLite database auto-creates at `./go-mail-data/data/go-mail.db`
- Schema migrations handled automatically
- Use structured logging for database operations